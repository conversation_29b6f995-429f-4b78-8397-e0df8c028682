{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./agent-allocation-gap.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./agent-allocation-gap.component.scss?ngResource\";\nimport { Component, inject, ViewChild } from \"@angular/core\";\nimport { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { ACMP, AcmService } from \"src/app/shared\";\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\nimport { HierarchyFormFieldComponent } from \"src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component\";\nimport { HierarchyFormDirective } from \"src/app/shared/directives/hierarchy-form.directive\";\nimport { ReportService } from '../../reports.service';\nimport { of } from 'rxjs';\nimport { ToastrService } from \"ngx-toastr\";\nimport { TypeaheadModule } from 'ngx-bootstrap/typeahead';\nimport { UserService } from 'src/app/authentication/user.service';\nlet AgentAllocationGapComponent = class AgentAllocationGapComponent {\n  constructor() {\n    this.acmService = inject(AcmService);\n    this.reportService = inject(ReportService);\n    this.toastr = inject(ToastrService);\n    this.userService = inject(UserService);\n    this.breadcrumbData = [{\n      label: \"Reports\"\n    }, {\n      label: \"Allocation Reports\"\n    }, {\n      label: \"Agent Allocation Gap Report\"\n    }];\n    this.canDownloadReport = this.acmService.hasACMAccess([ACMP.CanDownloadAgentAllocationGapReport]);\n    this.loader = {\n      generateReport: false,\n      downloadReport: false\n    };\n    // Radio button and field properties\n    this.reportType = 'bank';\n    this.agencyUser = false;\n    // Owner/Staff properties\n    this.staff = '';\n    this.bankUserList = of([]);\n    this.ownertypeaheadLoading = false;\n    // Branch properties\n    this.branchName = '';\n    this.basebranches = [];\n    this.branchnoResult = false;\n    this.branchtypeaheadLoading = false;\n    // Agency properties\n    this.AgencyName = '';\n    this.agencyList = [];\n    this.agencynoResult = false;\n    this.agencytypeaheadLoading = false;\n  }\n  ngOnInit() {\n    this.buildFormGroup();\n    this.initializeData();\n  }\n  ngAfterViewInit() {\n    this.searchForm.addControl(\"products\", this.productHierarchy.formGroup);\n    this.searchForm.addControl(\"buckets\", this.bucketHierarchy.formGroup);\n    this.searchForm.addControl(\"geos\", this.geoHierarchy.formGroup);\n    this.searchForm.updateValueAndValidity();\n  }\n  buildFormGroup() {\n    this.searchForm = new FormGroup({\n      allocationOwners: new FormControl([])\n    });\n  }\n  initializeData() {\n    // Get user details and check if agency user\n    try {\n      this.userDetails = JSON.parse(window.localStorage['currentUser'] || '{}');\n      console.log('User details loaded:', this.userDetails); // Debug log\n    } catch (error) {\n      console.error('Error parsing user details:', error);\n      this.userDetails = {};\n    }\n    // Initialize base branches\n    this.reportService.getBaseBranches().subscribe(basebranches => {\n      this.basebranches = basebranches;\n      console.log('Base branches loaded:', this.basebranches); // Debug log\n    }, err => this.toastr.error(err, \"Error!\"));\n    // Initialize agency list\n    this.getAgencyList();\n  }\n  resetvaluesNew() {\n    // Reset form values when radio button changes\n    this.staff = '';\n    this.branchName = '';\n    this.AgencyName = '';\n  }\n  generatePayload() {\n    const fValue = this.searchForm?.value;\n    const otherKeys = Object.keys(fValue)?.filter(k => ![\"products\", \"geos\", \"buckets\"]?.includes(k));\n    const obj = {\n      products: {\n        levels: Object.entries(fValue?.products)?.map(o => {\n          const [levelId, masterId] = o;\n          return {\n            levelId,\n            masterId\n          };\n        })\n      },\n      geos: {\n        levels: Object.entries(fValue?.geos)?.map(o => {\n          const [levelId, masterId] = o;\n          return {\n            levelId,\n            masterId\n          };\n        })\n      },\n      buckets: fValue?.buckets?.bucket\n    };\n    otherKeys?.forEach(k => obj[k] = fValue?.[k]);\n    // Add radio button selection and field values\n    obj.isCompanyuser = this.reportType === \"bank\";\n    // Add owner/staff information\n    if (this.staff) {\n      const staffParts = this.staff.split(' - ');\n      obj.owner = staffParts.length > 1 ? staffParts[1] : this.staff;\n    }\n    // Add branch or agency based on selection\n    if (this.reportType === 'bank' && this.branchName) {\n      obj.branch = this.branchName;\n    } else if (this.reportType === 'agency' && this.AgencyName) {\n      const agencyParts = this.AgencyName.split(' - ');\n      obj.agency = agencyParts.length > 1 ? agencyParts[1] : this.AgencyName;\n    }\n    return obj;\n  }\n  // Supervisor/Owner methods\n  getSupervisorList(term = '') {\n    if (!term || term.trim().length < 1) {\n      this.bankUserList = of([]);\n      return;\n    }\n    this.bankUserList = this.reportService.getSupervisor(term);\n  }\n  onSelectStaff(event) {\n    this.staff = `${event.item.firstName} - ${event.item.agencyCode}`;\n  }\n  ownerNameEmpty() {\n    this.staff = '';\n  }\n  typeaheadNoResults(event) {\n    if (event) {\n      this.toastr.info(\"Please enter correct supervising manager\", \"Info!\");\n    }\n  }\n  // Branch methods\n  onBranchSelect(event) {\n    this.branchName = event.item.name;\n  }\n  branchNameEmpty() {\n    this.branchName = '';\n  }\n  branchNoResults(event) {\n    this.branchnoResult = event;\n  }\n  branchChangeLoading(event) {\n    this.branchtypeaheadLoading = event;\n  }\n  // Agency methods\n  getAgencyList() {\n    this.reportService.getFieldTeleAgencyName().subscribe(response => {\n      this.agencyList = response;\n      console.log('Agency list loaded:', this.agencyList); // Debug log\n      const agencyRoles = [\"AgencyToBackEndExternalBIAP\", \"AgencyToFrontEndExternalBIAP\", \"AgencyToFrontEndExternalFOS\", \"AgencyToFrontEndExternalTC\"];\n      const role = this.userService.getPrimaryRole();\n      if (agencyRoles.includes(role)) {\n        this.agencyUser = true;\n        this.reportType = \"agency\";\n        let abc = this.userDetails.agencyFirstName + \" \" + this.userDetails.agencyLastName + \" \" + this.userDetails.agencyCode;\n        this.AgencyName = abc.replace('null', \"\");\n      } else {\n        this.agencyUser = false;\n      }\n    }, err => {\n      console.error('Error loading agency list:', err);\n      this.toastr.error(err, \"Error!\");\n    });\n  }\n  onAgencySelect(event) {\n    this.AgencyName = `${event.item.firstName}-${event.item.agencyCode}`;\n  }\n  agencyNameEmpty() {\n    this.AgencyName = '';\n  }\n  agencyNoResults(event) {\n    this.agencynoResult = event;\n  }\n  agencyChangeLoading(event) {\n    this.agencytypeaheadLoading = event;\n  }\n  generateReport() {\n    const payload = this.generatePayload();\n    console.log(payload);\n    this.loader.generateReport = true;\n    this.reportService.generateAgentGap(payload).subscribe({\n      next: response => {\n        this.loader.generateReport = false;\n        // Handle response output here (e.g., display report results)\n        console.log('Report data:', response);\n      },\n      error: err => {\n        this.loader.generateReport = false;\n        this.toastr.error(err?.message || 'Something went wrong', 'Error!');\n      }\n    });\n  }\n  downloadReport() {\n    const payload = this.generatePayload();\n    this.loader.downloadReport = true;\n    this.reportService.downloadAgentGapAllocation(payload).subscribe({\n      next: response => {\n        this.loader.downloadReport = false;\n        // Handle file download\n        this.downloadFile(response);\n      },\n      error: err => {\n        this.loader.downloadReport = false;\n        this.toastr.error(err?.message || 'Something went wrong', 'Error!');\n      }\n    });\n  }\n  downloadFile(response) {\n    const mediaType = 'application/zip';\n    const a = document.createElement(\"a\");\n    a.setAttribute('style', 'display:none;');\n    document.body.appendChild(a);\n    const blob = new Blob([response], {\n      type: mediaType\n    });\n    const url = window.URL.createObjectURL(blob);\n    a.href = url;\n    a.download = 'agentgap.zip';\n    a.click();\n    document.body.removeChild(a);\n    window.URL.revokeObjectURL(url);\n  }\n  static {\n    this.propDecorators = {\n      productHierarchy: [{\n        type: ViewChild,\n        args: [\"pr\"]\n      }],\n      bucketHierarchy: [{\n        type: ViewChild,\n        args: [\"buc\"]\n      }],\n      geoHierarchy: [{\n        type: ViewChild,\n        args: [\"geo\"]\n      }]\n    };\n  }\n};\nAgentAllocationGapComponent = __decorate([Component({\n  selector: \"app-agent-allocation-gap\",\n  standalone: true,\n  imports: [FormsModule, ReactiveFormsModule, BreadcrumbComponent, HierarchyFormDirective, HierarchyFormFieldComponent, TypeaheadModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AgentAllocationGapComponent);\nexport { AgentAllocationGapComponent };", "map": {"version": 3, "names": ["Component", "inject", "ViewChild", "FormControl", "FormGroup", "FormsModule", "ReactiveFormsModule", "ACMP", "AcmService", "BreadcrumbComponent", "HierarchyFormFieldComponent", "HierarchyFormDirective", "ReportService", "of", "ToastrService", "TypeaheadModule", "UserService", "AgentAllocationGapComponent", "constructor", "acmService", "reportService", "toastr", "userService", "breadcrumbData", "label", "canDownloadReport", "hasACMAccess", "CanDownloadAgentAllocationGapReport", "loader", "generateReport", "downloadReport", "reportType", "agencyUser", "staff", "bankUserList", "ownertypeaheadLoading", "branchName", "basebranches", "branchnoResult", "branchtypeaheadLoading", "AgencyName", "agencyList", "agencynoResult", "agencytypeaheadLoading", "ngOnInit", "buildFormGroup", "initializeData", "ngAfterViewInit", "searchForm", "addControl", "productHierarchy", "formGroup", "bucketHierarchy", "geoHierarchy", "updateValueAndValidity", "allocationOwners", "userDetails", "JSON", "parse", "window", "localStorage", "console", "log", "error", "getBaseBranches", "subscribe", "err", "getAgencyList", "resetvalues<PERSON>ew", "generatePayload", "fValue", "value", "otherKeys", "Object", "keys", "filter", "k", "includes", "obj", "products", "levels", "entries", "map", "o", "levelId", "masterId", "geos", "buckets", "bucket", "for<PERSON>ach", "isCompanyuser", "staffParts", "split", "owner", "length", "branch", "agencyParts", "agency", "getSupervisorList", "term", "trim", "getSupervisor", "onSelectStaff", "event", "item", "firstName", "agencyCode", "ownerNameEmpty", "typeaheadNoResults", "info", "onBranchSelect", "name", "branchNameEmpty", "branchNoResults", "branchChangeLoading", "getFieldTeleAgencyName", "response", "agencyRoles", "role", "getPrimaryRole", "abc", "agencyFirstName", "agencyLastName", "replace", "onAgencySelect", "agencyNameEmpty", "agencyNoResults", "agencyChangeLoading", "payload", "generateAgentGap", "next", "message", "downloadAgentGapAllocation", "downloadFile", "mediaType", "a", "document", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "blob", "Blob", "type", "url", "URL", "createObjectURL", "href", "download", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\github\\sowreports\\SCB-ENCollect.FE.Sleek\\src\\app\\reports\\pages\\agent-allocation-gap\\agent-allocation-gap.component.ts"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  Component,\r\n  inject,\r\n  OnInit,\r\n  ViewChild,\r\n} from \"@angular/core\";\r\nimport {\r\n  FormControl,\r\n  FormGroup,\r\n  FormsModule,\r\n  ReactiveFormsModule,\r\n} from \"@angular/forms\";\r\nimport { ACMP, AcmService } from \"src/app/shared\";\r\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\r\nimport { HierarchyFormFieldComponent } from \"src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component\";\r\nimport { HierarchyFormDirective } from \"src/app/shared/directives/hierarchy-form.directive\";\r\nimport { ReportService } from '../../reports.service';\r\nimport { Observable, of } from 'rxjs';\r\nimport { mergeMap, map } from 'rxjs/operators';\r\nimport { ToastrService } from \"ngx-toastr\";\r\nimport { TypeaheadModule } from 'ngx-bootstrap/typeahead';\r\nimport { UserService } from 'src/app/authentication/user.service';\r\n\r\n\r\n@Component({\r\n   selector: \"app-agent-allocation-gap\",\r\n  standalone: true,\r\n  imports: [\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    BreadcrumbComponent,\r\n    HierarchyFormDirective,\r\n    HierarchyFormFieldComponent,\r\n    TypeaheadModule,\r\n  ],\r\n  templateUrl: \"./agent-allocation-gap.component.html\",\r\n  styleUrl: \"./agent-allocation-gap.component.scss\",\r\n})\r\nexport class AgentAllocationGapComponent implements OnInit, AfterViewInit {\r\n  private acmService = inject(AcmService);\r\n  private reportService = inject(ReportService);\r\n  private toastr = inject(ToastrService);\r\n  private userService = inject(UserService);\r\n  @ViewChild(\"pr\") productHierarchy: HierarchyFormDirective;\r\n  @ViewChild(\"buc\") bucketHierarchy: HierarchyFormDirective;\r\n  @ViewChild(\"geo\") geoHierarchy: HierarchyFormDirective;\r\n\r\n  breadcrumbData = [\r\n    { label: \"Reports\" },\r\n    { label: \"Allocation Reports\" },\r\n    { label: \"Agent Allocation Gap Report\" },\r\n  ];\r\n  canDownloadReport = this.acmService.hasACMAccess([\r\n    ACMP.CanDownloadAgentAllocationGapReport,\r\n  ]);\r\n  loader = {\r\n    generateReport: false,\r\n    downloadReport: false,\r\n  };\r\n  searchForm!: FormGroup;\r\n\r\n  // Radio button and field properties\r\n  reportType: string = 'bank';\r\n  agencyUser: boolean = false;\r\n\r\n  // Owner/Staff properties\r\n  staff: string = '';\r\n  bankUserList: Observable<any[]> = of([]);\r\n  ownertypeaheadLoading: boolean = false;\r\n\r\n  // Branch properties\r\n  branchName: string = '';\r\n  basebranches: any[] = [];\r\n  branchnoResult: boolean = false;\r\n  branchtypeaheadLoading: boolean = false;\r\n\r\n  // Agency properties\r\n  AgencyName: string = '';\r\n  agencyList: any[] = [];\r\n  agencynoResult: boolean = false;\r\n  agencytypeaheadLoading: boolean = false;\r\n\r\n  // User details\r\n  userDetails: any;\r\n\r\n  ngOnInit(): void {\r\n    this.buildFormGroup();\r\n    this.initializeData();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.searchForm.addControl(\"products\", this.productHierarchy.formGroup);\r\n    this.searchForm.addControl(\"buckets\", this.bucketHierarchy.formGroup);\r\n    this.searchForm.addControl(\"geos\", this.geoHierarchy.formGroup);\r\n    this.searchForm.updateValueAndValidity();\r\n  }\r\n\r\n  buildFormGroup() {\r\n    this.searchForm = new FormGroup({\r\n      allocationOwners: new FormControl([]),\r\n    });\r\n  }\r\n\r\n  initializeData(): void {\r\n    // Get user details and check if agency user\r\n    try {\r\n      this.userDetails = JSON.parse(window.localStorage['currentUser'] || '{}');\r\n      console.log('User details loaded:', this.userDetails); // Debug log\r\n    } catch (error) {\r\n      console.error('Error parsing user details:', error);\r\n      this.userDetails = {};\r\n    }\r\n\r\n    // Initialize base branches\r\n    this.reportService.getBaseBranches().subscribe(\r\n      basebranches => {\r\n        this.basebranches = basebranches;\r\n        console.log('Base branches loaded:', this.basebranches); // Debug log\r\n      },\r\n      err => this.toastr.error(err, \"Error!\")\r\n    );\r\n\r\n    // Initialize agency list\r\n    this.getAgencyList();\r\n  }\r\n\r\n  resetvaluesNew(): void {\r\n    // Reset form values when radio button changes\r\n    this.staff = '';\r\n    this.branchName = '';\r\n    this.AgencyName = '';\r\n  }\r\n\r\n  generatePayload() {\r\n    const fValue = this.searchForm?.value;\r\n    const otherKeys = Object.keys(fValue)?.filter(\r\n      (k) => ![\"products\", \"geos\", \"buckets\"]?.includes(k)\r\n    );\r\n    const obj: any = {\r\n      products: {\r\n        levels: Object.entries(fValue?.products)?.map((o) => {\r\n          const [levelId, masterId] = o;\r\n          return { levelId, masterId };\r\n        }),\r\n      },\r\n      geos: {\r\n        levels: Object.entries(fValue?.geos)?.map((o) => {\r\n          const [levelId, masterId] = o;\r\n          return { levelId, masterId };\r\n        }),\r\n      },\r\n      buckets: fValue?.buckets?.bucket,\r\n    };\r\n\r\n    otherKeys?.forEach((k) => (obj[k] = fValue?.[k]));\r\n\r\n    // Add radio button selection and field values\r\n    obj.isCompanyuser = this.reportType === \"bank\";\r\n\r\n    // Add owner/staff information\r\n    if (this.staff) {\r\n      const staffParts = this.staff.split(' - ');\r\n      obj.owner = staffParts.length > 1 ? staffParts[1] : this.staff;\r\n    }\r\n\r\n    // Add branch or agency based on selection\r\n    if (this.reportType === 'bank' && this.branchName) {\r\n      obj.branch = this.branchName;\r\n    } else if (this.reportType === 'agency' && this.AgencyName) {\r\n      const agencyParts = this.AgencyName.split(' - ');\r\n      obj.agency = agencyParts.length > 1 ? agencyParts[1] : this.AgencyName;\r\n    }\r\n\r\n    return obj;\r\n  }\r\n\r\n  // Supervisor/Owner methods\r\n  getSupervisorList(term: string = ''): void {\r\n    if (!term || term.trim().length < 1) {\r\n      this.bankUserList = of([]);\r\n      return;\r\n    }\r\n    this.bankUserList = this.reportService.getSupervisor(term);\r\n  }\r\n\r\n  onSelectStaff(event: any): void {\r\n    this.staff = `${event.item.firstName} - ${event.item.agencyCode}`;\r\n  }\r\n\r\n  ownerNameEmpty(): void {\r\n    this.staff = '';\r\n  }\r\n\r\n  typeaheadNoResults(event: boolean): void {\r\n    if (event) {\r\n      this.toastr.info(\"Please enter correct supervising manager\", \"Info!\");\r\n    }\r\n  }\r\n\r\n  // Branch methods\r\n  onBranchSelect(event: any): void {\r\n    this.branchName = event.item.name;\r\n  }\r\n\r\n  branchNameEmpty(): void {\r\n    this.branchName = '';\r\n  }\r\n\r\n  branchNoResults(event: boolean): void {\r\n    this.branchnoResult = event;\r\n  }\r\n\r\n  branchChangeLoading(event: boolean): void {\r\n    this.branchtypeaheadLoading = event;\r\n  }\r\n\r\n  // Agency methods\r\n  getAgencyList(): void {\r\n    this.reportService.getFieldTeleAgencyName().subscribe(\r\n      response => {\r\n        this.agencyList = response;\r\n        console.log('Agency list loaded:', this.agencyList); // Debug log\r\n        const agencyRoles = [\"AgencyToBackEndExternalBIAP\", \"AgencyToFrontEndExternalBIAP\", \"AgencyToFrontEndExternalFOS\", \"AgencyToFrontEndExternalTC\"];\r\n        const role = this.userService.getPrimaryRole();\r\n        if (agencyRoles.includes(role)) {\r\n          this.agencyUser = true;\r\n          this.reportType = \"agency\";\r\n          let abc = this.userDetails.agencyFirstName + \" \" + this.userDetails.agencyLastName + \" \" + this.userDetails.agencyCode;\r\n          this.AgencyName = abc.replace('null', \"\");\r\n        } else {\r\n          this.agencyUser = false;\r\n        }\r\n      },\r\n      err => {\r\n        console.error('Error loading agency list:', err);\r\n        this.toastr.error(err, \"Error!\");\r\n      }\r\n    );\r\n  }\r\n\r\n  onAgencySelect(event: any): void {\r\n    this.AgencyName = `${event.item.firstName}-${event.item.agencyCode}`;\r\n  }\r\n\r\n  agencyNameEmpty(): void {\r\n    this.AgencyName = '';\r\n  }\r\n\r\n  agencyNoResults(event: boolean): void {\r\n    this.agencynoResult = event;\r\n  }\r\n\r\n  agencyChangeLoading(event: boolean): void {\r\n    this.agencytypeaheadLoading = event;\r\n  }\r\n\r\n generateReport() {\r\n  const payload = this.generatePayload();\r\n  console.log(payload);\r\n\r\n  this.loader.generateReport = true;\r\n\r\n  this.reportService.generateAgentGap(payload).subscribe({\r\n    next: (response) => {\r\n      this.loader.generateReport = false;\r\n      // Handle response output here (e.g., display report results)\r\n      console.log('Report data:', response);\r\n    },\r\n    error: (err) => {\r\n      this.loader.generateReport = false;\r\n      this.toastr.error(err?.message || 'Something went wrong', 'Error!');\r\n    }\r\n  });\r\n}\r\n\r\n  downloadReport(): void {\r\n    const payload = this.generatePayload();\r\n    this.loader.downloadReport = true;\r\n\r\n    this.reportService.downloadAgentGapAllocation(payload).subscribe({\r\n      next: (response) => {\r\n        this.loader.downloadReport = false;\r\n        // Handle file download\r\n        this.downloadFile(response);\r\n      },\r\n      error: (err) => {\r\n        this.loader.downloadReport = false;\r\n        this.toastr.error(err?.message || 'Something went wrong', 'Error!');\r\n      }\r\n    });\r\n  }\r\n\r\n  private downloadFile(response: any): void {\r\n    const mediaType = 'application/zip';\r\n    const a = document.createElement(\"a\");\r\n    a.setAttribute('style', 'display:none;');\r\n    document.body.appendChild(a);\r\n    const blob = new Blob([response], { type: mediaType });\r\n    const url = window.URL.createObjectURL(blob);\r\n    a.href = url;\r\n    a.download = 'agentgap.zip';\r\n    a.click();\r\n    document.body.removeChild(a);\r\n    window.URL.revokeObjectURL(url);\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAEEA,SAAS,EACTC,MAAM,EAENC,SAAS,QACJ,eAAe;AACtB,SACEC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,mBAAmB,QACd,gBAAgB;AACvB,SAASC,IAAI,EAAEC,UAAU,QAAQ,gBAAgB;AACjD,SAASC,mBAAmB,QAAQ,2DAA2D;AAC/F,SAASC,2BAA2B,QAAQ,+EAA+E;AAC3H,SAASC,sBAAsB,QAAQ,oDAAoD;AAC3F,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAAqBC,EAAE,QAAQ,MAAM;AAErC,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,WAAW,QAAQ,qCAAqC;AAiB1D,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EAAjCC,YAAA;IACG,KAAAC,UAAU,GAAGlB,MAAM,CAACO,UAAU,CAAC;IAC/B,KAAAY,aAAa,GAAGnB,MAAM,CAACW,aAAa,CAAC;IACrC,KAAAS,MAAM,GAAGpB,MAAM,CAACa,aAAa,CAAC;IAC9B,KAAAQ,WAAW,GAAGrB,MAAM,CAACe,WAAW,CAAC;IAKzC,KAAAO,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAS,CAAE,EACpB;MAAEA,KAAK,EAAE;IAAoB,CAAE,EAC/B;MAAEA,KAAK,EAAE;IAA6B,CAAE,CACzC;IACD,KAAAC,iBAAiB,GAAG,IAAI,CAACN,UAAU,CAACO,YAAY,CAAC,CAC/CnB,IAAI,CAACoB,mCAAmC,CACzC,CAAC;IACF,KAAAC,MAAM,GAAG;MACPC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE;KACjB;IAGD;IACA,KAAAC,UAAU,GAAW,MAAM;IAC3B,KAAAC,UAAU,GAAY,KAAK;IAE3B;IACA,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,YAAY,GAAsBrB,EAAE,CAAC,EAAE,CAAC;IACxC,KAAAsB,qBAAqB,GAAY,KAAK;IAEtC;IACA,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,sBAAsB,GAAY,KAAK;IAEvC;IACA,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,sBAAsB,GAAY,KAAK;EAiOzC;EA5NEC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,CAACC,UAAU,CAAC,UAAU,EAAE,IAAI,CAACC,gBAAgB,CAACC,SAAS,CAAC;IACvE,IAAI,CAACH,UAAU,CAACC,UAAU,CAAC,SAAS,EAAE,IAAI,CAACG,eAAe,CAACD,SAAS,CAAC;IACrE,IAAI,CAACH,UAAU,CAACC,UAAU,CAAC,MAAM,EAAE,IAAI,CAACI,YAAY,CAACF,SAAS,CAAC;IAC/D,IAAI,CAACH,UAAU,CAACM,sBAAsB,EAAE;EAC1C;EAEAT,cAAcA,CAAA;IACZ,IAAI,CAACG,UAAU,GAAG,IAAI5C,SAAS,CAAC;MAC9BmD,gBAAgB,EAAE,IAAIpD,WAAW,CAAC,EAAE;KACrC,CAAC;EACJ;EAEA2C,cAAcA,CAAA;IACZ;IACA,IAAI;MACF,IAAI,CAACU,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,YAAY,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;MACzEC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACN,WAAW,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,IAAI,CAACP,WAAW,GAAG,EAAE;IACvB;IAEA;IACA,IAAI,CAACpC,aAAa,CAAC4C,eAAe,EAAE,CAACC,SAAS,CAC5C5B,YAAY,IAAG;MACb,IAAI,CAACA,YAAY,GAAGA,YAAY;MAChCwB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACzB,YAAY,CAAC,CAAC,CAAC;IAC3D,CAAC,EACD6B,GAAG,IAAI,IAAI,CAAC7C,MAAM,CAAC0C,KAAK,CAACG,GAAG,EAAE,QAAQ,CAAC,CACxC;IAED;IACA,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACnC,KAAK,GAAG,EAAE;IACf,IAAI,CAACG,UAAU,GAAG,EAAE;IACpB,IAAI,CAACI,UAAU,GAAG,EAAE;EACtB;EAEA6B,eAAeA,CAAA;IACb,MAAMC,MAAM,GAAG,IAAI,CAACtB,UAAU,EAAEuB,KAAK;IACrC,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,EAAEK,MAAM,CAC1CC,CAAC,IAAK,CAAC,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,CAAC,EAAEC,QAAQ,CAACD,CAAC,CAAC,CACrD;IACD,MAAME,GAAG,GAAQ;MACfC,QAAQ,EAAE;QACRC,MAAM,EAAEP,MAAM,CAACQ,OAAO,CAACX,MAAM,EAAES,QAAQ,CAAC,EAAEG,GAAG,CAAEC,CAAC,IAAI;UAClD,MAAM,CAACC,OAAO,EAAEC,QAAQ,CAAC,GAAGF,CAAC;UAC7B,OAAO;YAAEC,OAAO;YAAEC;UAAQ,CAAE;QAC9B,CAAC;OACF;MACDC,IAAI,EAAE;QACJN,MAAM,EAAEP,MAAM,CAACQ,OAAO,CAACX,MAAM,EAAEgB,IAAI,CAAC,EAAEJ,GAAG,CAAEC,CAAC,IAAI;UAC9C,MAAM,CAACC,OAAO,EAAEC,QAAQ,CAAC,GAAGF,CAAC;UAC7B,OAAO;YAAEC,OAAO;YAAEC;UAAQ,CAAE;QAC9B,CAAC;OACF;MACDE,OAAO,EAAEjB,MAAM,EAAEiB,OAAO,EAAEC;KAC3B;IAEDhB,SAAS,EAAEiB,OAAO,CAAEb,CAAC,IAAME,GAAG,CAACF,CAAC,CAAC,GAAGN,MAAM,GAAGM,CAAC,CAAE,CAAC;IAEjD;IACAE,GAAG,CAACY,aAAa,GAAG,IAAI,CAAC3D,UAAU,KAAK,MAAM;IAE9C;IACA,IAAI,IAAI,CAACE,KAAK,EAAE;MACd,MAAM0D,UAAU,GAAG,IAAI,CAAC1D,KAAK,CAAC2D,KAAK,CAAC,KAAK,CAAC;MAC1Cd,GAAG,CAACe,KAAK,GAAGF,UAAU,CAACG,MAAM,GAAG,CAAC,GAAGH,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC1D,KAAK;IAChE;IAEA;IACA,IAAI,IAAI,CAACF,UAAU,KAAK,MAAM,IAAI,IAAI,CAACK,UAAU,EAAE;MACjD0C,GAAG,CAACiB,MAAM,GAAG,IAAI,CAAC3D,UAAU;IAC9B,CAAC,MAAM,IAAI,IAAI,CAACL,UAAU,KAAK,QAAQ,IAAI,IAAI,CAACS,UAAU,EAAE;MAC1D,MAAMwD,WAAW,GAAG,IAAI,CAACxD,UAAU,CAACoD,KAAK,CAAC,KAAK,CAAC;MAChDd,GAAG,CAACmB,MAAM,GAAGD,WAAW,CAACF,MAAM,GAAG,CAAC,GAAGE,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAACxD,UAAU;IACxE;IAEA,OAAOsC,GAAG;EACZ;EAEA;EACAoB,iBAAiBA,CAACC,IAAA,GAAe,EAAE;IACjC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE,CAACN,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAAC5D,YAAY,GAAGrB,EAAE,CAAC,EAAE,CAAC;MAC1B;IACF;IACA,IAAI,CAACqB,YAAY,GAAG,IAAI,CAACd,aAAa,CAACiF,aAAa,CAACF,IAAI,CAAC;EAC5D;EAEAG,aAAaA,CAACC,KAAU;IACtB,IAAI,CAACtE,KAAK,GAAG,GAAGsE,KAAK,CAACC,IAAI,CAACC,SAAS,MAAMF,KAAK,CAACC,IAAI,CAACE,UAAU,EAAE;EACnE;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC1E,KAAK,GAAG,EAAE;EACjB;EAEA2E,kBAAkBA,CAACL,KAAc;IAC/B,IAAIA,KAAK,EAAE;MACT,IAAI,CAAClF,MAAM,CAACwF,IAAI,CAAC,0CAA0C,EAAE,OAAO,CAAC;IACvE;EACF;EAEA;EACAC,cAAcA,CAACP,KAAU;IACvB,IAAI,CAACnE,UAAU,GAAGmE,KAAK,CAACC,IAAI,CAACO,IAAI;EACnC;EAEAC,eAAeA,CAAA;IACb,IAAI,CAAC5E,UAAU,GAAG,EAAE;EACtB;EAEA6E,eAAeA,CAACV,KAAc;IAC5B,IAAI,CAACjE,cAAc,GAAGiE,KAAK;EAC7B;EAEAW,mBAAmBA,CAACX,KAAc;IAChC,IAAI,CAAChE,sBAAsB,GAAGgE,KAAK;EACrC;EAEA;EACApC,aAAaA,CAAA;IACX,IAAI,CAAC/C,aAAa,CAAC+F,sBAAsB,EAAE,CAAClD,SAAS,CACnDmD,QAAQ,IAAG;MACT,IAAI,CAAC3E,UAAU,GAAG2E,QAAQ;MAC1BvD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACrB,UAAU,CAAC,CAAC,CAAC;MACrD,MAAM4E,WAAW,GAAG,CAAC,6BAA6B,EAAE,8BAA8B,EAAE,6BAA6B,EAAE,4BAA4B,CAAC;MAChJ,MAAMC,IAAI,GAAG,IAAI,CAAChG,WAAW,CAACiG,cAAc,EAAE;MAC9C,IAAIF,WAAW,CAACxC,QAAQ,CAACyC,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACtF,UAAU,GAAG,IAAI;QACtB,IAAI,CAACD,UAAU,GAAG,QAAQ;QAC1B,IAAIyF,GAAG,GAAG,IAAI,CAAChE,WAAW,CAACiE,eAAe,GAAG,GAAG,GAAG,IAAI,CAACjE,WAAW,CAACkE,cAAc,GAAG,GAAG,GAAG,IAAI,CAAClE,WAAW,CAACkD,UAAU;QACtH,IAAI,CAAClE,UAAU,GAAGgF,GAAG,CAACG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;MAC3C,CAAC,MAAM;QACL,IAAI,CAAC3F,UAAU,GAAG,KAAK;MACzB;IACF,CAAC,EACDkC,GAAG,IAAG;MACJL,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEG,GAAG,CAAC;MAChD,IAAI,CAAC7C,MAAM,CAAC0C,KAAK,CAACG,GAAG,EAAE,QAAQ,CAAC;IAClC,CAAC,CACF;EACH;EAEA0D,cAAcA,CAACrB,KAAU;IACvB,IAAI,CAAC/D,UAAU,GAAG,GAAG+D,KAAK,CAACC,IAAI,CAACC,SAAS,IAAIF,KAAK,CAACC,IAAI,CAACE,UAAU,EAAE;EACtE;EAEAmB,eAAeA,CAAA;IACb,IAAI,CAACrF,UAAU,GAAG,EAAE;EACtB;EAEAsF,eAAeA,CAACvB,KAAc;IAC5B,IAAI,CAAC7D,cAAc,GAAG6D,KAAK;EAC7B;EAEAwB,mBAAmBA,CAACxB,KAAc;IAChC,IAAI,CAAC5D,sBAAsB,GAAG4D,KAAK;EACrC;EAED1E,cAAcA,CAAA;IACb,MAAMmG,OAAO,GAAG,IAAI,CAAC3D,eAAe,EAAE;IACtCR,OAAO,CAACC,GAAG,CAACkE,OAAO,CAAC;IAEpB,IAAI,CAACpG,MAAM,CAACC,cAAc,GAAG,IAAI;IAEjC,IAAI,CAACT,aAAa,CAAC6G,gBAAgB,CAACD,OAAO,CAAC,CAAC/D,SAAS,CAAC;MACrDiE,IAAI,EAAGd,QAAQ,IAAI;QACjB,IAAI,CAACxF,MAAM,CAACC,cAAc,GAAG,KAAK;QAClC;QACAgC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEsD,QAAQ,CAAC;MACvC,CAAC;MACDrD,KAAK,EAAGG,GAAG,IAAI;QACb,IAAI,CAACtC,MAAM,CAACC,cAAc,GAAG,KAAK;QAClC,IAAI,CAACR,MAAM,CAAC0C,KAAK,CAACG,GAAG,EAAEiE,OAAO,IAAI,sBAAsB,EAAE,QAAQ,CAAC;MACrE;KACD,CAAC;EACJ;EAEErG,cAAcA,CAAA;IACZ,MAAMkG,OAAO,GAAG,IAAI,CAAC3D,eAAe,EAAE;IACtC,IAAI,CAACzC,MAAM,CAACE,cAAc,GAAG,IAAI;IAEjC,IAAI,CAACV,aAAa,CAACgH,0BAA0B,CAACJ,OAAO,CAAC,CAAC/D,SAAS,CAAC;MAC/DiE,IAAI,EAAGd,QAAQ,IAAI;QACjB,IAAI,CAACxF,MAAM,CAACE,cAAc,GAAG,KAAK;QAClC;QACA,IAAI,CAACuG,YAAY,CAACjB,QAAQ,CAAC;MAC7B,CAAC;MACDrD,KAAK,EAAGG,GAAG,IAAI;QACb,IAAI,CAACtC,MAAM,CAACE,cAAc,GAAG,KAAK;QAClC,IAAI,CAACT,MAAM,CAAC0C,KAAK,CAACG,GAAG,EAAEiE,OAAO,IAAI,sBAAsB,EAAE,QAAQ,CAAC;MACrE;KACD,CAAC;EACJ;EAEQE,YAAYA,CAACjB,QAAa;IAChC,MAAMkB,SAAS,GAAG,iBAAiB;IACnC,MAAMC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,YAAY,CAAC,OAAO,EAAE,eAAe,CAAC;IACxCF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,CAAC,CAAC;IAC5B,MAAMM,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC1B,QAAQ,CAAC,EAAE;MAAE2B,IAAI,EAAET;IAAS,CAAE,CAAC;IACtD,MAAMU,GAAG,GAAGrF,MAAM,CAACsF,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAC5CN,CAAC,CAACY,IAAI,GAAGH,GAAG;IACZT,CAAC,CAACa,QAAQ,GAAG,cAAc;IAC3Bb,CAAC,CAACc,KAAK,EAAE;IACTb,QAAQ,CAACG,IAAI,CAACW,WAAW,CAACf,CAAC,CAAC;IAC5B5E,MAAM,CAACsF,GAAG,CAACM,eAAe,CAACP,GAAG,CAAC;EACjC;;;;cArQC9I,SAAS;QAAAsJ,IAAA,GAAC,IAAI;MAAA;;cACdtJ,SAAS;QAAAsJ,IAAA,GAAC,KAAK;MAAA;;cACftJ,SAAS;QAAAsJ,IAAA,GAAC,KAAK;MAAA;;;;AAPLvI,2BAA2B,GAAAwI,UAAA,EAdvCzJ,SAAS,CAAC;EACR0J,QAAQ,EAAE,0BAA0B;EACrCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPvJ,WAAW,EACXC,mBAAmB,EACnBG,mBAAmB,EACnBE,sBAAsB,EACtBD,2BAA2B,EAC3BK,eAAe,CAChB;EACD8I,QAAA,EAAAC,oBAAoD;;CAErD,CAAC,C,EACW7I,2BAA2B,CA2QvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}