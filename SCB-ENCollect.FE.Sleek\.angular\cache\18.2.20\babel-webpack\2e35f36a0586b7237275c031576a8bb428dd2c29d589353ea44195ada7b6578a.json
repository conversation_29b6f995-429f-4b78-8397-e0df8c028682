{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./agent-allocation-gap.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./agent-allocation-gap.component.scss?ngResource\";\nimport { Component, inject, ViewChild } from \"@angular/core\";\nimport { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { ACMP, AcmService } from \"src/app/shared\";\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\nimport { HierarchyFormFieldComponent } from \"src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component\";\nimport { HierarchyFormDirective } from \"src/app/shared/directives/hierarchy-form.directive\";\nimport { ReportService } from '../../reports.service';\nimport { of } from 'rxjs';\nimport { ToastrService } from \"ngx-toastr\";\nimport { TypeaheadModule } from 'ngx-bootstrap/typeahead';\nimport { UserService } from 'src/app/authentication/user.service';\nlet AgentAllocationGapComponent = class AgentAllocationGapComponent {\n  constructor() {\n    this.acmService = inject(AcmService);\n    this.reportService = inject(ReportService);\n    this.toastr = inject(ToastrService);\n    this.userService = inject(UserService);\n    this.breadcrumbData = [{\n      label: \"Reports\"\n    }, {\n      label: \"Allocation Reports\"\n    }, {\n      label: \"Agent Allocation Gap Report\"\n    }];\n    this.canDownloadReport = this.acmService.hasACMAccess([ACMP.CanDownloadAgentAllocationGapReport]);\n    this.loader = {\n      generateReport: false,\n      downloadReport: false\n    };\n    // Radio button and field properties\n    this.reportType = 'bank';\n    this.agencyUser = false;\n    // Owner/Staff properties\n    this.staff = '';\n    this.bankUserList = of([]);\n    this.ownertypeaheadLoading = false;\n    // Branch properties\n    this.branchName = '';\n    this.basebranches = [];\n    this.branchnoResult = false;\n    this.branchtypeaheadLoading = false;\n    // Agency properties\n    this.AgencyName = '';\n    this.agencyList = [];\n    this.agencynoResult = false;\n    this.agencytypeaheadLoading = false;\n  }\n  ngOnInit() {\n    this.buildFormGroup();\n    this.initializeData();\n  }\n  ngAfterViewInit() {\n    this.searchForm.addControl(\"products\", this.productHierarchy.formGroup);\n    this.searchForm.addControl(\"buckets\", this.bucketHierarchy.formGroup);\n    this.searchForm.addControl(\"geos\", this.geoHierarchy.formGroup);\n    this.searchForm.updateValueAndValidity();\n  }\n  buildFormGroup() {\n    this.searchForm = new FormGroup({\n      allocationOwners: new FormControl([])\n    });\n  }\n  generatePayload() {\n    const fValue = this.searchForm?.value;\n    const otherKeys = Object.keys(fValue)?.filter(k => ![\"products\", \"geos\", \"buckets\"]?.includes(k));\n    const obj = {\n      products: {\n        levels: Object.entries(fValue?.products)?.map(o => {\n          const [levelId, masterId] = o;\n          return {\n            levelId,\n            masterId\n          };\n        })\n      },\n      geos: {\n        levels: Object.entries(fValue?.geos)?.map(o => {\n          const [levelId, masterId] = o;\n          return {\n            levelId,\n            masterId\n          };\n        })\n      },\n      buckets: fValue?.buckets?.bucket\n    };\n    otherKeys?.forEach(k => obj[k] = fValue?.[k]);\n    return obj;\n  }\n  generateReport() {\n    const payload = this.generatePayload();\n    console.log(payload);\n    this.loader.generateReport = true;\n    this.reportService.generateAgentGap(payload).subscribe({\n      next: response => {\n        // this.loader.generateReport = false;\n        // Handle response output here (e.g., display report results)\n        // console.log('Report data:', response.output);\n      },\n      error: err => {\n        // this.loader.generateReport = false;\n        // this.toastr.error(err?.message || 'Something went wrong', 'Error!');\n      }\n    });\n  }\n  static {\n    this.propDecorators = {\n      productHierarchy: [{\n        type: ViewChild,\n        args: [\"pr\"]\n      }],\n      bucketHierarchy: [{\n        type: ViewChild,\n        args: [\"buc\"]\n      }],\n      geoHierarchy: [{\n        type: ViewChild,\n        args: [\"geo\"]\n      }]\n    };\n  }\n};\nAgentAllocationGapComponent = __decorate([Component({\n  selector: \"app-agent-allocation-gap\",\n  standalone: true,\n  imports: [FormsModule, ReactiveFormsModule, BreadcrumbComponent, HierarchyFormDirective, HierarchyFormFieldComponent, TypeaheadModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AgentAllocationGapComponent);\nexport { AgentAllocationGapComponent };", "map": {"version": 3, "names": ["Component", "inject", "ViewChild", "FormControl", "FormGroup", "FormsModule", "ReactiveFormsModule", "ACMP", "AcmService", "BreadcrumbComponent", "HierarchyFormFieldComponent", "HierarchyFormDirective", "ReportService", "of", "ToastrService", "TypeaheadModule", "UserService", "AgentAllocationGapComponent", "constructor", "acmService", "reportService", "toastr", "userService", "breadcrumbData", "label", "canDownloadReport", "hasACMAccess", "CanDownloadAgentAllocationGapReport", "loader", "generateReport", "downloadReport", "reportType", "agencyUser", "staff", "bankUserList", "ownertypeaheadLoading", "branchName", "basebranches", "branchnoResult", "branchtypeaheadLoading", "AgencyName", "agencyList", "agencynoResult", "agencytypeaheadLoading", "ngOnInit", "buildFormGroup", "initializeData", "ngAfterViewInit", "searchForm", "addControl", "productHierarchy", "formGroup", "bucketHierarchy", "geoHierarchy", "updateValueAndValidity", "allocationOwners", "generatePayload", "fValue", "value", "otherKeys", "Object", "keys", "filter", "k", "includes", "obj", "products", "levels", "entries", "map", "o", "levelId", "masterId", "geos", "buckets", "bucket", "for<PERSON>ach", "payload", "console", "log", "generateAgentGap", "subscribe", "next", "response", "error", "err", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\github\\sowreports\\SCB-ENCollect.FE.Sleek\\src\\app\\reports\\pages\\agent-allocation-gap\\agent-allocation-gap.component.ts"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  Component,\r\n  inject,\r\n  OnInit,\r\n  ViewChild,\r\n} from \"@angular/core\";\r\nimport {\r\n  FormControl,\r\n  FormGroup,\r\n  FormsModule,\r\n  ReactiveFormsModule,\r\n} from \"@angular/forms\";\r\nimport { ACMP, AcmService } from \"src/app/shared\";\r\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\r\nimport { HierarchyFormFieldComponent } from \"src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component\";\r\nimport { HierarchyFormDirective } from \"src/app/shared/directives/hierarchy-form.directive\";\r\nimport { ReportService } from '../../reports.service';\r\nimport { Observable, of } from 'rxjs';\r\nimport { mergeMap, map } from 'rxjs/operators';\r\nimport { ToastrService } from \"ngx-toastr\";\r\nimport { TypeaheadModule } from 'ngx-bootstrap/typeahead';\r\nimport { UserService } from 'src/app/authentication/user.service';\r\n\r\n\r\n@Component({\r\n   selector: \"app-agent-allocation-gap\",\r\n  standalone: true,\r\n  imports: [\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    BreadcrumbComponent,\r\n    HierarchyFormDirective,\r\n    HierarchyFormFieldComponent,\r\n    TypeaheadModule,\r\n  ],\r\n  templateUrl: \"./agent-allocation-gap.component.html\",\r\n  styleUrl: \"./agent-allocation-gap.component.scss\",\r\n})\r\nexport class AgentAllocationGapComponent implements OnInit, AfterViewInit {\r\n  private acmService = inject(AcmService);\r\n  private reportService = inject(ReportService);\r\n  private toastr = inject(ToastrService);\r\n  private userService = inject(UserService);\r\n  @ViewChild(\"pr\") productHierarchy: HierarchyFormDirective;\r\n  @ViewChild(\"buc\") bucketHierarchy: HierarchyFormDirective;\r\n  @ViewChild(\"geo\") geoHierarchy: HierarchyFormDirective;\r\n\r\n  breadcrumbData = [\r\n    { label: \"Reports\" },\r\n    { label: \"Allocation Reports\" },\r\n    { label: \"Agent Allocation Gap Report\" },\r\n  ];\r\n  canDownloadReport = this.acmService.hasACMAccess([\r\n    ACMP.CanDownloadAgentAllocationGapReport,\r\n  ]);\r\n  loader = {\r\n    generateReport: false,\r\n    downloadReport: false,\r\n  };\r\n  searchForm!: FormGroup;\r\n\r\n  // Radio button and field properties\r\n  reportType: string = 'bank';\r\n  agencyUser: boolean = false;\r\n\r\n  // Owner/Staff properties\r\n  staff: string = '';\r\n  bankUserList: Observable<any[]> = of([]);\r\n  ownertypeaheadLoading: boolean = false;\r\n\r\n  // Branch properties\r\n  branchName: string = '';\r\n  basebranches: any[] = [];\r\n  branchnoResult: boolean = false;\r\n  branchtypeaheadLoading: boolean = false;\r\n\r\n  // Agency properties\r\n  AgencyName: string = '';\r\n  agencyList: any[] = [];\r\n  agencynoResult: boolean = false;\r\n  agencytypeaheadLoading: boolean = false;\r\n\r\n  // User details\r\n  userDetails: any;\r\n\r\n  ngOnInit(): void {\r\n    this.buildFormGroup();\r\n    this.initializeData();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.searchForm.addControl(\"products\", this.productHierarchy.formGroup);\r\n    this.searchForm.addControl(\"buckets\", this.bucketHierarchy.formGroup);\r\n    this.searchForm.addControl(\"geos\", this.geoHierarchy.formGroup);\r\n    this.searchForm.updateValueAndValidity();\r\n  }\r\n\r\n  buildFormGroup() {\r\n    this.searchForm = new FormGroup({\r\n      allocationOwners: new FormControl([]),\r\n    });\r\n  }\r\n\r\n  generatePayload() {\r\n    const fValue = this.searchForm?.value;\r\n    const otherKeys = Object.keys(fValue)?.filter(\r\n      (k) => ![\"products\", \"geos\", \"buckets\"]?.includes(k)\r\n    );\r\n    const obj = {\r\n      products: {\r\n        levels: Object.entries(fValue?.products)?.map((o) => {\r\n          const [levelId, masterId] = o;\r\n          return { levelId, masterId };\r\n        }),\r\n      },\r\n      geos: {\r\n        levels: Object.entries(fValue?.geos)?.map((o) => {\r\n          const [levelId, masterId] = o;\r\n          return { levelId, masterId };\r\n        }),\r\n      },\r\n      buckets: fValue?.buckets?.bucket,\r\n    };\r\n\r\n    otherKeys?.forEach((k) => (obj[k] = fValue?.[k]));\r\n\r\n    return obj;\r\n  }\r\n\r\n generateReport() {\r\n  const payload = this.generatePayload();\r\n  console.log(payload);\r\n\r\n  this.loader.generateReport = true;\r\n\r\n  this.reportService.generateAgentGap(payload).subscribe({\r\n    next: (response) => {\r\n      // this.loader.generateReport = false;\r\n\r\n      \r\n\r\n      // Handle response output here (e.g., display report results)\r\n      // console.log('Report data:', response.output);\r\n    },\r\n    error: (err) => {\r\n      // this.loader.generateReport = false;\r\n      // this.toastr.error(err?.message || 'Something went wrong', 'Error!');\r\n    }\r\n  });\r\n}\r\n\r\n\r\n  \r\n}\r\n"], "mappings": ";;;AAAA,SAEEA,SAAS,EACTC,MAAM,EAENC,SAAS,QACJ,eAAe;AACtB,SACEC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,mBAAmB,QACd,gBAAgB;AACvB,SAASC,IAAI,EAAEC,UAAU,QAAQ,gBAAgB;AACjD,SAASC,mBAAmB,QAAQ,2DAA2D;AAC/F,SAASC,2BAA2B,QAAQ,+EAA+E;AAC3H,SAASC,sBAAsB,QAAQ,oDAAoD;AAC3F,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAAqBC,EAAE,QAAQ,MAAM;AAErC,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,WAAW,QAAQ,qCAAqC;AAiB1D,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EAAjCC,YAAA;IACG,KAAAC,UAAU,GAAGlB,MAAM,CAACO,UAAU,CAAC;IAC/B,KAAAY,aAAa,GAAGnB,MAAM,CAACW,aAAa,CAAC;IACrC,KAAAS,MAAM,GAAGpB,MAAM,CAACa,aAAa,CAAC;IAC9B,KAAAQ,WAAW,GAAGrB,MAAM,CAACe,WAAW,CAAC;IAKzC,KAAAO,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAS,CAAE,EACpB;MAAEA,KAAK,EAAE;IAAoB,CAAE,EAC/B;MAAEA,KAAK,EAAE;IAA6B,CAAE,CACzC;IACD,KAAAC,iBAAiB,GAAG,IAAI,CAACN,UAAU,CAACO,YAAY,CAAC,CAC/CnB,IAAI,CAACoB,mCAAmC,CACzC,CAAC;IACF,KAAAC,MAAM,GAAG;MACPC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE;KACjB;IAGD;IACA,KAAAC,UAAU,GAAW,MAAM;IAC3B,KAAAC,UAAU,GAAY,KAAK;IAE3B;IACA,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,YAAY,GAAsBrB,EAAE,CAAC,EAAE,CAAC;IACxC,KAAAsB,qBAAqB,GAAY,KAAK;IAEtC;IACA,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,sBAAsB,GAAY,KAAK;IAEvC;IACA,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,sBAAsB,GAAY,KAAK;EAyEzC;EApEEC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,CAACC,UAAU,CAAC,UAAU,EAAE,IAAI,CAACC,gBAAgB,CAACC,SAAS,CAAC;IACvE,IAAI,CAACH,UAAU,CAACC,UAAU,CAAC,SAAS,EAAE,IAAI,CAACG,eAAe,CAACD,SAAS,CAAC;IACrE,IAAI,CAACH,UAAU,CAACC,UAAU,CAAC,MAAM,EAAE,IAAI,CAACI,YAAY,CAACF,SAAS,CAAC;IAC/D,IAAI,CAACH,UAAU,CAACM,sBAAsB,EAAE;EAC1C;EAEAT,cAAcA,CAAA;IACZ,IAAI,CAACG,UAAU,GAAG,IAAI5C,SAAS,CAAC;MAC9BmD,gBAAgB,EAAE,IAAIpD,WAAW,CAAC,EAAE;KACrC,CAAC;EACJ;EAEAqD,eAAeA,CAAA;IACb,MAAMC,MAAM,GAAG,IAAI,CAACT,UAAU,EAAEU,KAAK;IACrC,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,EAAEK,MAAM,CAC1CC,CAAC,IAAK,CAAC,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,CAAC,EAAEC,QAAQ,CAACD,CAAC,CAAC,CACrD;IACD,MAAME,GAAG,GAAG;MACVC,QAAQ,EAAE;QACRC,MAAM,EAAEP,MAAM,CAACQ,OAAO,CAACX,MAAM,EAAES,QAAQ,CAAC,EAAEG,GAAG,CAAEC,CAAC,IAAI;UAClD,MAAM,CAACC,OAAO,EAAEC,QAAQ,CAAC,GAAGF,CAAC;UAC7B,OAAO;YAAEC,OAAO;YAAEC;UAAQ,CAAE;QAC9B,CAAC;OACF;MACDC,IAAI,EAAE;QACJN,MAAM,EAAEP,MAAM,CAACQ,OAAO,CAACX,MAAM,EAAEgB,IAAI,CAAC,EAAEJ,GAAG,CAAEC,CAAC,IAAI;UAC9C,MAAM,CAACC,OAAO,EAAEC,QAAQ,CAAC,GAAGF,CAAC;UAC7B,OAAO;YAAEC,OAAO;YAAEC;UAAQ,CAAE;QAC9B,CAAC;OACF;MACDE,OAAO,EAAEjB,MAAM,EAAEiB,OAAO,EAAEC;KAC3B;IAEDhB,SAAS,EAAEiB,OAAO,CAAEb,CAAC,IAAME,GAAG,CAACF,CAAC,CAAC,GAAGN,MAAM,GAAGM,CAAC,CAAE,CAAC;IAEjD,OAAOE,GAAG;EACZ;EAEDpC,cAAcA,CAAA;IACb,MAAMgD,OAAO,GAAG,IAAI,CAACrB,eAAe,EAAE;IACtCsB,OAAO,CAACC,GAAG,CAACF,OAAO,CAAC;IAEpB,IAAI,CAACjD,MAAM,CAACC,cAAc,GAAG,IAAI;IAEjC,IAAI,CAACT,aAAa,CAAC4D,gBAAgB,CAACH,OAAO,CAAC,CAACI,SAAS,CAAC;MACrDC,IAAI,EAAGC,QAAQ,IAAI;QACjB;QAIA;QACA;MAAA,CACD;MACDC,KAAK,EAAGC,GAAG,IAAI;QACb;QACA;MAAA;KAEH,CAAC;EACJ;;;;cA1GGnF,SAAS;QAAAoF,IAAA,GAAC,IAAI;MAAA;;cACdpF,SAAS;QAAAoF,IAAA,GAAC,KAAK;MAAA;;cACfpF,SAAS;QAAAoF,IAAA,GAAC,KAAK;MAAA;;;;AAPLrE,2BAA2B,GAAAsE,UAAA,EAdvCvF,SAAS,CAAC;EACRwF,QAAQ,EAAE,0BAA0B;EACrCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPrF,WAAW,EACXC,mBAAmB,EACnBG,mBAAmB,EACnBE,sBAAsB,EACtBD,2BAA2B,EAC3BK,eAAe,CAChB;EACD4E,QAAA,EAAAC,oBAAoD;;CAErD,CAAC,C,EACW3E,2BAA2B,CAmHvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}