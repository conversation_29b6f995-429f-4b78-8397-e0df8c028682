
<div class="inner-layout-container">
  <app-breadcrumb [data]="breadcrumbData"> </app-breadcrumb>
 <h2 class="title">Agent Allocation Gap Report</h2>


  <div class="enc-card">
    <div class="card-content" [formGroup]="searchForm">
      <div class="row">
        <div class="form-control-group col-md-12 col-lg-4">
          <label class="form-label required">User Type </label>
          <div class="form-radio-group with-box">
            @if (!agencyUser) {
              <label for="inlineRadio1">
                <input
                  type="radio"
                  name="inlineRadioOptions"
                  id="inlineRadio1"
                  value="bank"
                  [(ngModel)]="reportType"
                  (change)="resetvaluesNew()"
                  checked="true"
                  required
                />
                Branch
              </label>
            }
            <label for="inlineRadio2">
              <input
                type="radio"
                name="inlineRadioOptions"
                id="inlineRadio2"
                value="agency"
                [(ngModel)]="reportType"
                (change)="resetvaluesNew()"
                required
              />
              Agency
            </label>
          </div>
        </div>
      </div>
      <div class="row">
        <ng-container
          appHierarchyForm
          #pr="appHierarchyForm"
          [group]="'Product'"
          [required]="true"
        >
          @for (level of pr?.levels; let i = $index; track level.id) {
          <app-hierarchy-form-field
            [level]="level"
            [hierarchyForm]="pr"
            [index]="i"
            class="col-lg-4 col-md-6"
          >
          </app-hierarchy-form-field>
          }
        </ng-container>
        <ng-container
          appHierarchyForm
          #buc="appHierarchyForm"
          [group]="'Bucket'"
          [required]="true"
        >
          @for (level of buc?.levels; let i = $index; track level.id) {
          <app-hierarchy-form-field
            [level]="level"
            [hierarchyForm]="buc"
            [index]="i"
            class="col-lg-4 col-md-6"
          >
          </app-hierarchy-form-field>
          }
        </ng-container>
        <ng-container
          appHierarchyForm
          #geo="appHierarchyForm"
          [group]="'Geo'"
          [required]="true"
        >
          @for (level of geo?.levels; let i = $index; track level.id) {
          <app-hierarchy-form-field
            [level]="level"
            [hierarchyForm]="geo"
            [index]="i"
            class="col-lg-4 col-md-6"
          >
          </app-hierarchy-form-field>
          }
        </ng-container>

        <div class="form-control-group col-lg-4 col-md-6 col-sm-4">
          <label class="form-label">Owner </label>
          @if (ownertypeaheadLoading) {
            <span>
              <img src="assets/images/loader.gif" height="20px" width="20px" />
            </span>
          }
          <input
            class="form-control"
            id="bank_staff"
            (blur)="ownerNameEmpty()"
            [typeaheadItemTemplate]="customItemTemplate"
            [typeaheadAsync]="true"
            [(ngModel)]="staff"
            (click)="getSupervisorList()"
            name="bankStaff"
            (typeaheadNoResults)="typeaheadNoResults($event)"
            [typeaheadScrollable]="true"
            [typeahead]="bankUserList"
            [container]="'body'"
            [typeaheadOptionsInScrollableView]="5"
            typeaheadOptionField="firstName"
            (typeaheadOnSelect)="onSelectStaff($event)"
          />
        </div>

        @if (reportType == 'bank') {
          <div class="form-control-group col-lg-4 col-md-6 col-sm-4">
            <label class="form-label">Branch </label>
            <input
              type="text"
              (blur)="branchNameEmpty()"
              name="branchName"
              id="branch_name"
              class="form-control"
              typeaheadOptionField="name"
              [(ngModel)]="branchName"
              [typeahead]="basebranches"
              [container]="'body'"
              [typeaheadHideResultsOnBlur]="false"
              [typeaheadScrollable]="true"
              [typeaheadOptionsInScrollableView]="5"
              (typeaheadNoResults)="branchNoResults($event)"
              (typeaheadLoading)="branchChangeLoading($event)"
              (typeaheadOnSelect)="onBranchSelect($event)"
            />
          </div>
        }
        @if (reportType == 'agency') {
          <div class="form-control-group col-lg-4 col-md-6 col-sm-4">
            <label class="form-label">Agency </label>
            <input
              type="text"
              (blur)="agencyNameEmpty()"
              [typeaheadItemTemplate]="customItemTemplate"
              name="agencyName"
              id="agency_name"
              class="form-control"
              typeaheadOptionField="firstName"
              [(ngModel)]="AgencyName"
              [typeahead]="agencyList"
              [container]="'body'"
              [typeaheadHideResultsOnBlur]="false"
              [typeaheadScrollable]="true"
              [typeaheadOptionsInScrollableView]="5"
              (typeaheadNoResults)="agencyNoResults($event)"
              (typeaheadLoading)="agencyChangeLoading($event)"
              (typeaheadOnSelect)="onAgencySelect($event)"
              [disabled]="agencyUser"
            />
          </div>
        }
         </div>
    </div>
    <div class="card-footer">
      <button
        id="generate-report-button"
        type="button"
        class="btn btn-secondary mw-150px"
        (click)="generateReport()"
        [disabled]="searchForm?.invalid"
      >
        Generate Report @if (loader.generateReport) {
        <span>
          <img src="assets/images/loader.gif" height="20px" width="20px" />
        </span>
        }
      </button>
      @if (canDownloadReport) {
      <button
        id="download-report-button"
        class="btn btn-outline-dark mw-150px ms-4"
        (click)="downloadReport()"
        [disabled]="searchForm?.invalid"
      >
        Download Report
      </button>
      }
    </div>
  </div>
</div>
<ng-template #customItemTemplate let-model="item" let-index="index">
  <div>{{ model.firstName }}-{{ model.agencyCode }}</div>
</ng-template>
