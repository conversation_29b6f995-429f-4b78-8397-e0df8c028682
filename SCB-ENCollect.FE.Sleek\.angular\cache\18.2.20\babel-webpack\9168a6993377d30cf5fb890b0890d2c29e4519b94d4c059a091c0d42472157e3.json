{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./agent-allocation-gap.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./agent-allocation-gap.component.scss?ngResource\";\nimport { Component, inject, ViewChild } from \"@angular/core\";\nimport { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { ACMP, AcmService } from \"src/app/shared\";\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\nimport { HierarchyFormFieldComponent } from \"src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component\";\nimport { HierarchyFormDirective } from \"src/app/shared/directives/hierarchy-form.directive\";\nimport { ReportService } from '../../reports.service';\nimport { ToastrService } from \"ngx-toastr\";\nimport { TypeaheadModule } from 'ngx-bootstrap/typeahead';\nimport { UserService } from 'src/app/authentication/user.service';\nlet AgentAllocationGapComponent = class AgentAllocationGapComponent {\n  constructor() {\n    this.acmService = inject(AcmService);\n    this.reportService = inject(ReportService);\n    this.toastr = inject(ToastrService);\n    this.userService = inject(UserService);\n    this.breadcrumbData = [{\n      label: \"Reports\"\n    }, {\n      label: \"Allocation Reports\"\n    }, {\n      label: \"Agent Allocation Gap Report\"\n    }];\n    this.canDownloadReport = this.acmService.hasACMAccess([ACMP.CanDownloadAgentAllocationGapReport]);\n    this.loader = {\n      generateReport: false,\n      downloadReport: false\n    };\n  }\n  ngOnInit() {\n    this.buildFormGroup();\n  }\n  ngAfterViewInit() {\n    this.searchForm.addControl(\"products\", this.productHierarchy.formGroup);\n    this.searchForm.addControl(\"buckets\", this.bucketHierarchy.formGroup);\n    this.searchForm.addControl(\"geos\", this.geoHierarchy.formGroup);\n    this.searchForm.updateValueAndValidity();\n  }\n  buildFormGroup() {\n    this.searchForm = new FormGroup({\n      allocationOwners: new FormControl([])\n    });\n  }\n  generatePayload() {\n    const fValue = this.searchForm?.value;\n    const otherKeys = Object.keys(fValue)?.filter(k => ![\"products\", \"geos\", \"buckets\"]?.includes(k));\n    const obj = {\n      products: {\n        levels: Object.entries(fValue?.products)?.map(o => {\n          const [levelId, masterId] = o;\n          return {\n            levelId,\n            masterId\n          };\n        })\n      },\n      geos: {\n        levels: Object.entries(fValue?.geos)?.map(o => {\n          const [levelId, masterId] = o;\n          return {\n            levelId,\n            masterId\n          };\n        })\n      },\n      buckets: fValue?.buckets?.bucket\n    };\n    otherKeys?.forEach(k => obj[k] = fValue?.[k]);\n    return obj;\n  }\n  generateReport() {\n    const payload = this.generatePayload();\n    console.log(payload);\n    this.loader.generateReport = true;\n    this.reportService.generateAgentGap(payload).subscribe({\n      next: response => {\n        // this.loader.generateReport = false;\n        // Handle response output here (e.g., display report results)\n        // console.log('Report data:', response.output);\n      },\n      error: err => {\n        // this.loader.generateReport = false;\n        // this.toastr.error(err?.message || 'Something went wrong', 'Error!');\n      }\n    });\n  }\n  static {\n    this.propDecorators = {\n      productHierarchy: [{\n        type: ViewChild,\n        args: [\"pr\"]\n      }],\n      bucketHierarchy: [{\n        type: ViewChild,\n        args: [\"buc\"]\n      }],\n      geoHierarchy: [{\n        type: ViewChild,\n        args: [\"geo\"]\n      }]\n    };\n  }\n};\nAgentAllocationGapComponent = __decorate([Component({\n  selector: \"app-agent-allocation-gap\",\n  standalone: true,\n  imports: [FormsModule, ReactiveFormsModule, BreadcrumbComponent, HierarchyFormDirective, HierarchyFormFieldComponent, TypeaheadModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AgentAllocationGapComponent);\nexport { AgentAllocationGapComponent };", "map": {"version": 3, "names": ["Component", "inject", "ViewChild", "FormControl", "FormGroup", "FormsModule", "ReactiveFormsModule", "ACMP", "AcmService", "BreadcrumbComponent", "HierarchyFormFieldComponent", "HierarchyFormDirective", "ReportService", "ToastrService", "TypeaheadModule", "UserService", "AgentAllocationGapComponent", "constructor", "acmService", "reportService", "toastr", "userService", "breadcrumbData", "label", "canDownloadReport", "hasACMAccess", "CanDownloadAgentAllocationGapReport", "loader", "generateReport", "downloadReport", "ngOnInit", "buildFormGroup", "ngAfterViewInit", "searchForm", "addControl", "productHierarchy", "formGroup", "bucketHierarchy", "geoHierarchy", "updateValueAndValidity", "allocationOwners", "generatePayload", "fValue", "value", "otherKeys", "Object", "keys", "filter", "k", "includes", "obj", "products", "levels", "entries", "map", "o", "levelId", "masterId", "geos", "buckets", "bucket", "for<PERSON>ach", "payload", "console", "log", "generateAgentGap", "subscribe", "next", "response", "error", "err", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\github\\sowreports\\SCB-ENCollect.FE.Sleek\\src\\app\\reports\\pages\\agent-allocation-gap\\agent-allocation-gap.component.ts"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  Component,\r\n  inject,\r\n  OnInit,\r\n  ViewChild,\r\n} from \"@angular/core\";\r\nimport {\r\n  FormControl,\r\n  FormGroup,\r\n  FormsModule,\r\n  ReactiveFormsModule,\r\n} from \"@angular/forms\";\r\nimport { ACMP, AcmService } from \"src/app/shared\";\r\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\r\nimport { HierarchyFormFieldComponent } from \"src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component\";\r\nimport { HierarchyFormDirective } from \"src/app/shared/directives/hierarchy-form.directive\";\r\nimport { ReportService } from '../../reports.service';\r\nimport { Observable, of } from 'rxjs';\r\nimport { mergeMap, map } from 'rxjs/operators';\r\nimport { ToastrService } from \"ngx-toastr\";\r\nimport { TypeaheadModule } from 'ngx-bootstrap/typeahead';\r\nimport { UserService } from 'src/app/authentication/user.service';\r\n\r\n\r\n@Component({\r\n   selector: \"app-agent-allocation-gap\",\r\n  standalone: true,\r\n  imports: [\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    BreadcrumbComponent,\r\n    HierarchyFormDirective,\r\n    HierarchyFormFieldComponent,\r\n    TypeaheadModule,\r\n  ],\r\n  templateUrl: \"./agent-allocation-gap.component.html\",\r\n  styleUrl: \"./agent-allocation-gap.component.scss\",\r\n})\r\nexport class AgentAllocationGapComponent implements OnInit, AfterViewInit {\r\n  private acmService = inject(AcmService);\r\n  private reportService = inject(ReportService);\r\n  private toastr = inject(ToastrService);\r\n  private userService = inject(UserService);\r\n  @ViewChild(\"pr\") productHierarchy: HierarchyFormDirective;\r\n  @ViewChild(\"buc\") bucketHierarchy: HierarchyFormDirective;\r\n  @ViewChild(\"geo\") geoHierarchy: HierarchyFormDirective;\r\n\r\n  breadcrumbData = [\r\n    { label: \"Reports\" },\r\n    { label: \"Allocation Reports\" },\r\n    { label: \"Agent Allocation Gap Report\" },\r\n  ];\r\n  canDownloadReport = this.acmService.hasACMAccess([\r\n    ACMP.CanDownloadAgentAllocationGapReport,\r\n  ]);\r\n  loader = {\r\n    generateReport: false,\r\n    downloadReport: false,\r\n  };\r\n  searchForm!: FormGroup;\r\n\r\n  ngOnInit(): void {\r\n    this.buildFormGroup();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.searchForm.addControl(\"products\", this.productHierarchy.formGroup);\r\n    this.searchForm.addControl(\"buckets\", this.bucketHierarchy.formGroup);\r\n    this.searchForm.addControl(\"geos\", this.geoHierarchy.formGroup);\r\n    this.searchForm.updateValueAndValidity();\r\n  }\r\n\r\n  buildFormGroup() {\r\n    this.searchForm = new FormGroup({\r\n      allocationOwners: new FormControl([]),\r\n    });\r\n  }\r\n\r\n  generatePayload() {\r\n    const fValue = this.searchForm?.value;\r\n    const otherKeys = Object.keys(fValue)?.filter(\r\n      (k) => ![\"products\", \"geos\", \"buckets\"]?.includes(k)\r\n    );\r\n    const obj = {\r\n      products: {\r\n        levels: Object.entries(fValue?.products)?.map((o) => {\r\n          const [levelId, masterId] = o;\r\n          return { levelId, masterId };\r\n        }),\r\n      },\r\n      geos: {\r\n        levels: Object.entries(fValue?.geos)?.map((o) => {\r\n          const [levelId, masterId] = o;\r\n          return { levelId, masterId };\r\n        }),\r\n      },\r\n      buckets: fValue?.buckets?.bucket,\r\n    };\r\n\r\n    otherKeys?.forEach((k) => (obj[k] = fValue?.[k]));\r\n\r\n    return obj;\r\n  }\r\n\r\n generateReport() {\r\n  const payload = this.generatePayload();\r\n  console.log(payload);\r\n\r\n  this.loader.generateReport = true;\r\n\r\n  this.reportService.generateAgentGap(payload).subscribe({\r\n    next: (response) => {\r\n      // this.loader.generateReport = false;\r\n\r\n      \r\n\r\n      // Handle response output here (e.g., display report results)\r\n      // console.log('Report data:', response.output);\r\n    },\r\n    error: (err) => {\r\n      // this.loader.generateReport = false;\r\n      // this.toastr.error(err?.message || 'Something went wrong', 'Error!');\r\n    }\r\n  });\r\n}\r\n\r\n\r\n  \r\n}\r\n"], "mappings": ";;;AAAA,SAEEA,SAAS,EACTC,MAAM,EAENC,SAAS,QACJ,eAAe;AACtB,SACEC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,mBAAmB,QACd,gBAAgB;AACvB,SAASC,IAAI,EAAEC,UAAU,QAAQ,gBAAgB;AACjD,SAASC,mBAAmB,QAAQ,2DAA2D;AAC/F,SAASC,2BAA2B,QAAQ,+EAA+E;AAC3H,SAASC,sBAAsB,QAAQ,oDAAoD;AAC3F,SAASC,aAAa,QAAQ,uBAAuB;AAGrD,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,WAAW,QAAQ,qCAAqC;AAiB1D,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EAAjCC,YAAA;IACG,KAAAC,UAAU,GAAGjB,MAAM,CAACO,UAAU,CAAC;IAC/B,KAAAW,aAAa,GAAGlB,MAAM,CAACW,aAAa,CAAC;IACrC,KAAAQ,MAAM,GAAGnB,MAAM,CAACY,aAAa,CAAC;IAC9B,KAAAQ,WAAW,GAAGpB,MAAM,CAACc,WAAW,CAAC;IAKzC,KAAAO,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAS,CAAE,EACpB;MAAEA,KAAK,EAAE;IAAoB,CAAE,EAC/B;MAAEA,KAAK,EAAE;IAA6B,CAAE,CACzC;IACD,KAAAC,iBAAiB,GAAG,IAAI,CAACN,UAAU,CAACO,YAAY,CAAC,CAC/ClB,IAAI,CAACmB,mCAAmC,CACzC,CAAC;IACF,KAAAC,MAAM,GAAG;MACPC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE;KACjB;EAsEH;EAnEEC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,CAACC,UAAU,CAAC,UAAU,EAAE,IAAI,CAACC,gBAAgB,CAACC,SAAS,CAAC;IACvE,IAAI,CAACH,UAAU,CAACC,UAAU,CAAC,SAAS,EAAE,IAAI,CAACG,eAAe,CAACD,SAAS,CAAC;IACrE,IAAI,CAACH,UAAU,CAACC,UAAU,CAAC,MAAM,EAAE,IAAI,CAACI,YAAY,CAACF,SAAS,CAAC;IAC/D,IAAI,CAACH,UAAU,CAACM,sBAAsB,EAAE;EAC1C;EAEAR,cAAcA,CAAA;IACZ,IAAI,CAACE,UAAU,GAAG,IAAI7B,SAAS,CAAC;MAC9BoC,gBAAgB,EAAE,IAAIrC,WAAW,CAAC,EAAE;KACrC,CAAC;EACJ;EAEAsC,eAAeA,CAAA;IACb,MAAMC,MAAM,GAAG,IAAI,CAACT,UAAU,EAAEU,KAAK;IACrC,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,EAAEK,MAAM,CAC1CC,CAAC,IAAK,CAAC,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,CAAC,EAAEC,QAAQ,CAACD,CAAC,CAAC,CACrD;IACD,MAAME,GAAG,GAAG;MACVC,QAAQ,EAAE;QACRC,MAAM,EAAEP,MAAM,CAACQ,OAAO,CAACX,MAAM,EAAES,QAAQ,CAAC,EAAEG,GAAG,CAAEC,CAAC,IAAI;UAClD,MAAM,CAACC,OAAO,EAAEC,QAAQ,CAAC,GAAGF,CAAC;UAC7B,OAAO;YAAEC,OAAO;YAAEC;UAAQ,CAAE;QAC9B,CAAC;OACF;MACDC,IAAI,EAAE;QACJN,MAAM,EAAEP,MAAM,CAACQ,OAAO,CAACX,MAAM,EAAEgB,IAAI,CAAC,EAAEJ,GAAG,CAAEC,CAAC,IAAI;UAC9C,MAAM,CAACC,OAAO,EAAEC,QAAQ,CAAC,GAAGF,CAAC;UAC7B,OAAO;YAAEC,OAAO;YAAEC;UAAQ,CAAE;QAC9B,CAAC;OACF;MACDE,OAAO,EAAEjB,MAAM,EAAEiB,OAAO,EAAEC;KAC3B;IAEDhB,SAAS,EAAEiB,OAAO,CAAEb,CAAC,IAAME,GAAG,CAACF,CAAC,CAAC,GAAGN,MAAM,GAAGM,CAAC,CAAE,CAAC;IAEjD,OAAOE,GAAG;EACZ;EAEDtB,cAAcA,CAAA;IACb,MAAMkC,OAAO,GAAG,IAAI,CAACrB,eAAe,EAAE;IACtCsB,OAAO,CAACC,GAAG,CAACF,OAAO,CAAC;IAEpB,IAAI,CAACnC,MAAM,CAACC,cAAc,GAAG,IAAI;IAEjC,IAAI,CAACT,aAAa,CAAC8C,gBAAgB,CAACH,OAAO,CAAC,CAACI,SAAS,CAAC;MACrDC,IAAI,EAAGC,QAAQ,IAAI;QACjB;QAIA;QACA;MAAA,CACD;MACDC,KAAK,EAAGC,GAAG,IAAI;QACb;QACA;MAAA;KAEH,CAAC;EACJ;;;;cAjFGpE,SAAS;QAAAqE,IAAA,GAAC,IAAI;MAAA;;cACdrE,SAAS;QAAAqE,IAAA,GAAC,KAAK;MAAA;;cACfrE,SAAS;QAAAqE,IAAA,GAAC,KAAK;MAAA;;;;AAPLvD,2BAA2B,GAAAwD,UAAA,EAdvCxE,SAAS,CAAC;EACRyE,QAAQ,EAAE,0BAA0B;EACrCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPtE,WAAW,EACXC,mBAAmB,EACnBG,mBAAmB,EACnBE,sBAAsB,EACtBD,2BAA2B,EAC3BI,eAAe,CAChB;EACD8D,QAAA,EAAAC,oBAAoD;;CAErD,CAAC,C,EACW7D,2BAA2B,CA0FvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}