import {
  AfterViewInit,
  Component,
  inject,
  OnInit,
  ViewChild,
} from "@angular/core";
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from "@angular/forms";
import { ACMP, AcmService } from "src/app/shared";
import { BreadcrumbComponent } from "src/app/shared/components/breadcrumb/breadcrumb.component";
import { HierarchyFormFieldComponent } from "src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component";
import { HierarchyFormDirective } from "src/app/shared/directives/hierarchy-form.directive";
import { ReportService } from '../../reports.service';
import { Observable, of } from 'rxjs';
import { mergeMap, map } from 'rxjs/operators';
import { ToastrService } from "ngx-toastr";
import { TypeaheadModule } from 'ngx-bootstrap/typeahead';
import { UserService } from 'src/app/authentication/user.service';


@Component({
   selector: "app-agent-allocation-gap",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    BreadcrumbComponent,
    HierarchyFormDirective,
    HierarchyFormFieldComponent,
    TypeaheadModule,
  ],
  templateUrl: "./agent-allocation-gap.component.html",
  styleUrl: "./agent-allocation-gap.component.scss",
})
export class AgentAllocationGapComponent implements OnInit, AfterViewInit {
  private acmService = inject(AcmService);
  private reportService = inject(ReportService);
  private toastr = inject(ToastrService);
  private userService = inject(UserService);
  @ViewChild("pr") productHierarchy: HierarchyFormDirective;
  @ViewChild("buc") bucketHierarchy: HierarchyFormDirective;
  @ViewChild("geo") geoHierarchy: HierarchyFormDirective;

  breadcrumbData = [
    { label: "Reports" },
    { label: "Allocation Reports" },
    { label: "Agent Allocation Gap Report" },
  ];
  canDownloadReport = this.acmService.hasACMAccess([
    ACMP.CanDownloadAgentAllocationGapReport,
  ]);
  loader = {
    generateReport: false,
    downloadReport: false,
  };
  searchForm!: FormGroup;

  // Radio button and field properties
  reportType: string = 'bank';
  agencyUser: boolean = false;

  // Owner/Staff properties
  staff: string = '';
  bankUserList: Observable<any[]> = of([]);
  ownertypeaheadLoading: boolean = false;

  // Branch properties
  branchName: string = '';
  basebranches: any[] = [];
  branchnoResult: boolean = false;
  branchtypeaheadLoading: boolean = false;

  // Agency properties
  AgencyName: string = '';
  agencyList: any[] = [];
  agencynoResult: boolean = false;
  agencytypeaheadLoading: boolean = false;

  // User details
  userDetails: any;

  ngOnInit(): void {
    this.buildFormGroup();
    this.initializeData();
  }

  ngAfterViewInit(): void {
    this.searchForm.addControl("products", this.productHierarchy.formGroup);
    this.searchForm.addControl("buckets", this.bucketHierarchy.formGroup);
    this.searchForm.addControl("geos", this.geoHierarchy.formGroup);
    this.searchForm.updateValueAndValidity();
  }

  buildFormGroup() {
    this.searchForm = new FormGroup({
      allocationOwners: new FormControl([]),
    });
  }

  initializeData(): void {
    // Get user details and check if agency user
    this.userDetails = JSON.parse(window.localStorage['currentUser'] || '{}');

    // Initialize base branches
    this.reportService.getBaseBranches().subscribe(
      basebranches => this.basebranches = basebranches,
      err => this.toastr.error(err, "Error!")
    );

    // Initialize agency list
    this.getAgencyList();
  }

  resetvaluesNew(): void {
    // Reset form values when radio button changes
    this.staff = '';
    this.branchName = '';
    this.AgencyName = '';
  }

  generatePayload() {
    const fValue = this.searchForm?.value;
    const otherKeys = Object.keys(fValue)?.filter(
      (k) => !["products", "geos", "buckets"]?.includes(k)
    );
    const obj: any = {
      products: {
        levels: Object.entries(fValue?.products)?.map((o) => {
          const [levelId, masterId] = o;
          return { levelId, masterId };
        }),
      },
      geos: {
        levels: Object.entries(fValue?.geos)?.map((o) => {
          const [levelId, masterId] = o;
          return { levelId, masterId };
        }),
      },
      buckets: fValue?.buckets?.bucket,
    };

    otherKeys?.forEach((k) => (obj[k] = fValue?.[k]));

    // Add radio button selection and field values
    obj.isCompanyuser = this.reportType === "bank";

    // Add owner/staff information
    if (this.staff) {
      const staffParts = this.staff.split(' - ');
      obj.owner = staffParts.length > 1 ? staffParts[1] : this.staff;
    }

    // Add branch or agency based on selection
    if (this.reportType === 'bank' && this.branchName) {
      obj.branch = this.branchName;
    } else if (this.reportType === 'agency' && this.AgencyName) {
      const agencyParts = this.AgencyName.split(' - ');
      obj.agency = agencyParts.length > 1 ? agencyParts[1] : this.AgencyName;
    }

    return obj;
  }

  // Supervisor/Owner methods
  getSupervisorList(term: string = ''): void {
    if (!term || term.trim().length < 1) {
      this.bankUserList = of([]);
      return;
    }
    this.bankUserList = this.reportService.getSupervisor(term);
  }

  onSelectStaff(event: any): void {
    this.staff = `${event.item.firstName} - ${event.item.agencyCode}`;
  }

  ownerNameEmpty(): void {
    this.staff = '';
  }

  typeaheadNoResults(event: boolean): void {
    if (event) {
      this.toastr.info("Please enter correct supervising manager", "Info!");
    }
  }

  // Branch methods
  onBranchSelect(event: any): void {
    this.branchName = event.item.name;
  }

  branchNameEmpty(): void {
    this.branchName = '';
  }

  branchNoResults(event: boolean): void {
    this.branchnoResult = event;
  }

  branchChangeLoading(event: boolean): void {
    this.branchtypeaheadLoading = event;
  }

  // Agency methods
  getAgencyList(): void {
    this.reportService.getFieldTeleAgencyName().subscribe(
      response => {
        this.agencyList = response;
        const agencyRoles = ["AgencyToBackEndExternalBIAP", "AgencyToFrontEndExternalBIAP", "AgencyToFrontEndExternalFOS", "AgencyToFrontEndExternalTC"];
        const role = this.userService.getPrimaryRole();
        if (agencyRoles.includes(role)) {
          this.agencyUser = true;
          this.reportType = "agency";
          let abc = this.userDetails.agencyFirstName + " " + this.userDetails.agencyLastName + " " + this.userDetails.agencyCode;
          this.AgencyName = abc.replace('null', "");
        }
      },
      err => this.toastr.error(err, "Error!")
    );
  }

  onAgencySelect(event: any): void {
    this.AgencyName = `${event.item.firstName} - ${event.item.agencyCode}`;
  }

  agencyNameEmpty(): void {
    this.AgencyName = '';
  }

  agencyNoResults(event: boolean): void {
    this.agencynoResult = event;
  }

  agencyChangeLoading(event: boolean): void {
    this.agencytypeaheadLoading = event;
  }

 generateReport() {
  const payload = this.generatePayload();
  console.log(payload);

  this.loader.generateReport = true;

  this.reportService.generateAgentGap(payload).subscribe({
    next: (response) => {
      this.loader.generateReport = false;
      // Handle response output here (e.g., display report results)
      console.log('Report data:', response);
    },
    error: (err) => {
      this.loader.generateReport = false;
      this.toastr.error(err?.message || 'Something went wrong', 'Error!');
    }
  });
}

  downloadReport(): void {
    const payload = this.generatePayload();
    this.loader.downloadReport = true;

    this.reportService.downloadAgentGapAllocation(payload).subscribe({
      next: (response) => {
        this.loader.downloadReport = false;
        // Handle file download
        this.downloadFile(response);
      },
      error: (err) => {
        this.loader.downloadReport = false;
        this.toastr.error(err?.message || 'Something went wrong', 'Error!');
      }
    });
  }

  private downloadFile(response: any): void {
    const mediaType = 'application/zip';
    const a = document.createElement("a");
    a.setAttribute('style', 'display:none;');
    document.body.appendChild(a);
    const blob = new Blob([response], { type: mediaType });
    const url = window.URL.createObjectURL(blob);
    a.href = url;
    a.download = 'agentgap.zip';
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }
}
